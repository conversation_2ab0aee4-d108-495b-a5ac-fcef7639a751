/// -----
/// internship_approval_detail_remote_data_source_impl.dart
///
/// 实习审批详情远程数据源实现
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter_demo/core/error/exceptions/server_exception.dart';
import 'package:flutter_demo/core/error/exceptions/network_exception.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/services/auth_expiry_service.dart';
import 'dart:async';

import '../../models/internship_approval_detail_response_model.dart';
import '../../models/internship_approval_request_model.dart';
import 'internship_approval_detail_remote_data_source.dart';

/// 实习审批详情远程数据源实现
/// 
/// 实现从远程API获取实习申请详情数据的具体逻辑
class InternshipApprovalDetailRemoteDataSourceImpl implements InternshipApprovalDetailRemoteDataSource {
  final DioClient _dioClient;
  
  static const String _tag = 'InternshipApprovalDetailRemoteDataSource';

  InternshipApprovalDetailRemoteDataSourceImpl(this._dioClient);

  @override
  Future<InternshipApprovalDetailResponseModel> getInternshipApprovalDetail(String id) async {
    try {
      Logger.info(_tag, '开始获取实习申请详情，ID: $id');
      
      final response = await _dioClient.get(
        'internshipservice/v1/internship/apply/teacher/detail',
        queryParameters: {
          'id': id,
        },
      );

      Logger.debug(_tag, '获取实习申请详情响应: $response');

      if (response != null) {
        // 构造完整的响应模型
        // 由于DioClient已经验证了resultCode，这里可以安全地假设请求成功
        return InternshipApprovalDetailResponseModel.fromJson(response as Map<String, dynamic>);
      } else {
        throw ServerException('响应数据为空');
      }
    } catch (e) {
      Logger.error(_tag, '获取实习申请详情失败: $e');

      // 检查是否是认证失效错误
      if (e is ServerException && e.message.contains('登陆信息失效')) {
        Logger.warning(_tag, '检测到认证失效，触发认证失效处理');

        // 使用依赖注入容器中的AuthExpiryService实例
        final authExpiryService = GetIt.instance<AuthExpiryService>();

        // 异步处理认证失效，不阻塞当前流程
        unawaited(authExpiryService.handleAuthExpiryWithoutContext(
          message: '登录已过期，请重新登录',
        ).catchError((error) {
          Logger.error(_tag, '处理认证失效时发生错误: $error');
        }));
      }

      if (e is ServerException || e is NetworkException) {
        rethrow;
      } else {
        throw ServerException('获取实习申请详情失败: $e');
      }
    }
  }

  @override
  Future<void> approveInternshipApplication(InternshipApprovalRequestModel request) async {
    try {
      Logger.info(_tag, '开始审批实习申请 - ID: ${request.id}, 状态: ${request.status}');

      await _dioClient.post(
        'internshipservice/v1/internship/teacher/apply/approve',
        data: request.toJson(),
      );

      Logger.info(_tag, '审批实习申请成功');
    } catch (e) {
      Logger.error(_tag, '审批实习申请失败: $e');

      // 检查是否是认证失效错误
      if (e is ServerException && e.message.contains('登陆信息失效')) {
        Logger.warning(_tag, '检测到认证失效，触发认证失效处理');

        // 使用依赖注入容器中的AuthExpiryService实例
        final authExpiryService = GetIt.instance<AuthExpiryService>();

        // 异步处理认证失效，不阻塞当前流程
        unawaited(authExpiryService.handleAuthExpiryWithoutContext(
          message: '登录已过期，请重新登录',
        ).catchError((error) {
          Logger.error(_tag, '处理认证失效时发生错误: $error');
        }));
      }

      if (e is ServerException || e is NetworkException) {
        rethrow;
      } else {
        throw ServerException('审批实习申请失败: $e');
      }
    }
  }
}
