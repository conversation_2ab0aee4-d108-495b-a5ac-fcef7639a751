/// -----
/// internship_approval_detail_event.dart
///
/// 实习审批详情事件定义
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 实习审批详情事件基类
abstract class InternshipApprovalDetailEvent extends Equatable {
  const InternshipApprovalDetailEvent();

  @override
  List<Object> get props => [];
}

/// 加载实习审批详情事件
class LoadInternshipApprovalDetailEvent extends InternshipApprovalDetailEvent {
  /// 实习申请ID
  final String id;

  /// 构造函数
  const LoadInternshipApprovalDetailEvent({
    required this.id,
  });

  @override
  List<Object> get props => [id];
}

/// 审批实习申请事件
class ApproveInternshipApplicationEvent extends InternshipApprovalDetailEvent {
  /// 实习申请ID
  final String id;
  
  /// 审批意见
  final String reviewOpinion;
  
  /// 是否通过（true=通过，false=驳回）
  final bool isApproved;

  /// 构造函数
  const ApproveInternshipApplicationEvent({
    required this.id,
    required this.reviewOpinion,
    required this.isApproved,
  });

  @override
  List<Object> get props => [id, reviewOpinion, isApproved];
}
