/// -----
/// internship_approval_response_model.dart
///
/// 实习申请审批API响应数据模型
/// 用于处理教师端实习申请列表接口的响应数据
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:intl/intl.dart';
import 'package:flutter_demo/features/internship/data/models/internship_application_model.dart';

/// 实习申请审批响应模型
///
/// 对应API接口：/v1/internship/apply/teacher/list
class InternshipApprovalResponseModel {
  /// 实习申请列表数据
  final List<InternshipApprovalItemModel> data;
  
  /// 结果代码
  final String resultCode;
  
  /// 结果消息
  final String resultMsg;

  const InternshipApprovalResponseModel({
    required this.data,
    required this.resultCode,
    required this.resultMsg,
  });

  /// 从JSON Map创建响应模型实例
  factory InternshipApprovalResponseModel.fromJson(Map<String, dynamic> json) {
    final dataList = json['data'] as List<dynamic>? ?? [];
    
    return InternshipApprovalResponseModel(
      data: dataList
          .map((item) => InternshipApprovalItemModel.fromJson(item as Map<String, dynamic>))
          .toList(),
      resultCode: json['resultCode'] ?? '',
      resultMsg: json['resultMsg'] ?? '',
    );
  }

  /// 转换为JSON Map
  Map<String, dynamic> toJson() {
    return {
      'data': data.map((item) => item.toJson()).toList(),
      'resultCode': resultCode,
      'resultMsg': resultMsg,
    };
  }

  /// 检查响应是否成功
  bool get isSuccess {
    return resultCode == '200' || resultCode == '0';
  }

  /// 获取错误消息
  String? get errorMessage {
    return isSuccess ? null : resultMsg;
  }
}

/// 实习申请审批项目模型
///
/// 对应API响应中的单个申请项目数据
class InternshipApprovalItemModel {
  /// 学生头像
  final String avatar;
  
  /// 企业名称
  final String companyName;
  
  /// 创建时间（时间戳）
  final int createTime;
  
  /// 实习申请ID
  final int id;
  
  /// 岗位部门
  final String jobDept;
  
  /// 岗位名称
  final String jobName;
  
  /// 学生ID
  final int studentId;
  
  /// 学生姓名
  final String studentName;

  const InternshipApprovalItemModel({
    required this.avatar,
    required this.companyName,
    required this.createTime,
    required this.id,
    required this.jobDept,
    required this.jobName,
    required this.studentId,
    required this.studentName,
  });

  /// 从JSON Map创建项目模型实例
  factory InternshipApprovalItemModel.fromJson(Map<String, dynamic> json) {
    return InternshipApprovalItemModel(
      avatar: json['avatar'] ?? '',
      companyName: json['companyName'] ?? '',
      createTime: json['createTime'] ?? 0,
      id: json['id'] ?? 0,
      jobDept: json['jobDept'] ?? '',
      jobName: json['jobName'] ?? '',
      studentId: json['studentId'] ?? 0,
      studentName: json['studentName'] ?? '',
    );
  }

  /// 转换为JSON Map
  Map<String, dynamic> toJson() {
    return {
      'avatar': avatar,
      'companyName': companyName,
      'createTime': createTime,
      'id': id,
      'jobDept': jobDept,
      'jobName': jobName,
      'studentId': studentId,
      'studentName': studentName,
    };
  }

  /// 转换为InternshipApplicationModel
  ///
  /// 将API响应数据映射到现有的UI模型
  InternshipApplicationModel toInternshipApplicationModel({required String status}) {
    return InternshipApplicationModel(
      id: id.toString(),
      studentName: studentName,
      studentAvatar: avatar.isNotEmpty ? avatar : 'assets/images/default_avatar.png',
      companyName: companyName,
      department: jobDept,
      position: jobName,
      applyDate: _formatTimestamp(createTime),
      status: status,
    );
  }

  /// 格式化时间戳为字符串
  ///
  /// 将时间戳转换为 "yyyy-MM-dd HH:mm" 格式
  String _formatTimestamp(int timestamp) {
    if (timestamp == 0) {
      return '';
    }
    
    try {
      // 处理时间戳（可能是秒或毫秒）
      final dateTime = timestamp > 1000000000000
          ? DateTime.fromMillisecondsSinceEpoch(timestamp)
          : DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
      
      return DateFormat('yyyy-MM-dd HH:mm').format(dateTime);
    } catch (e) {
      return '';
    }
  }
}
