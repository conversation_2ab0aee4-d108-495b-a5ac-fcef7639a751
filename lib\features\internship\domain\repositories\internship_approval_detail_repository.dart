/// -----
/// internship_approval_detail_repository.dart
///
/// 实习审批详情仓库抽象接口
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures/failure.dart';
import '../../data/models/internship_approval_detail_model.dart';
import '../entities/internship_approval_request.dart';

/// 实习审批详情仓库抽象接口
///
/// 定义实习申请详情和审批操作相关的数据操作方法
/// 使用Either类型处理成功和失败的情况
abstract class InternshipApprovalDetailRepository {
  /// 获取实习申请详情
  ///
  /// 根据实习申请ID获取详细信息
  ///
  /// 参数:
  ///   - [id]: 实习申请ID
  ///
  /// 返回:
  ///   Either<Failure, InternshipApprovalDetailModel> 成功返回申请详情，失败返回Failure
  Future<Either<Failure, InternshipApprovalDetailModel>> getInternshipApprovalDetail(String id);

  /// 审批实习申请
  ///
  /// 对实习申请进行通过或驳回操作
  ///
  /// 参数:
  ///   - [request]: 审批请求数据
  ///
  /// 返回:
  ///   Either<Failure, void> 成功返回void，失败返回Failure
  Future<Either<Failure, void>> approveInternshipApplication(InternshipApprovalRequest request);
}
