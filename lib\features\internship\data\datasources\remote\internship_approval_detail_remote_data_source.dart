/// -----
/// internship_approval_detail_remote_data_source.dart
///
/// 实习审批详情远程数据源接口
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import '../../models/internship_approval_detail_response_model.dart';
import '../../models/internship_approval_request_model.dart';

/// 实习审批详情远程数据源抽象接口
///
/// 定义获取实习申请详情和审批操作相关的远程数据操作方法
abstract class InternshipApprovalDetailRemoteDataSource {
  /// 获取实习申请详情
  ///
  /// 根据实习申请ID获取详细信息
  ///
  /// 参数:
  ///   - [id]: 实习申请ID
  ///
  /// 返回:
  ///   Future<InternshipApprovalDetailResponseModel> 申请详情响应结果
  ///
  /// 异常:
  ///   - ServerException: 服务器错误
  ///   - NetworkException: 网络错误
  ///   - UnauthorizedException: 认证失败
  Future<InternshipApprovalDetailResponseModel> getInternshipApprovalDetail(String id);

  /// 审批实习申请
  ///
  /// 对实习申请进行通过或驳回操作
  ///
  /// 参数:
  ///   - [request]: 审批请求数据
  ///
  /// 返回:
  ///   Future<void> 审批结果
  ///
  /// 异常:
  ///   - ServerException: 服务器错误
  ///   - NetworkException: 网络错误
  ///   - UnauthorizedException: 认证失败
  Future<void> approveInternshipApplication(InternshipApprovalRequestModel request);
}
