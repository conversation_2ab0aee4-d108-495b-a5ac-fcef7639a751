/// -----
/// internship_approval_list_bloc.dart
///
/// 实习申请审批列表BLoC
/// 管理教师端实习申请审批列表页面的状态和业务逻辑
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/features/internship/domain/usecases/get_internship_approval_list_usecase.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/internship_approval_list_event.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/internship_approval_list_state.dart';

/// 实习申请审批列表BLoC
///
/// 处理教师端实习申请审批列表页面的状态管理和业务逻辑
class InternshipApprovalListBloc extends Bloc<InternshipApprovalListEvent, InternshipApprovalListState> {
  /// 获取实习申请审批列表用例
  final GetInternshipApprovalListUseCase getInternshipApprovalListUseCase;

  /// 构造函数
  InternshipApprovalListBloc({
    required this.getInternshipApprovalListUseCase,
  }) : super(InternshipApprovalListInitial()) {
    // 注册事件处理器
    on<LoadInternshipApprovalListEvent>(_onLoadInternshipApprovalList);
    on<RefreshInternshipApprovalListEvent>(_onRefreshInternshipApprovalList);
  }

  /// 处理加载实习申请审批列表事件
  Future<void> _onLoadInternshipApprovalList(
    LoadInternshipApprovalListEvent event,
    Emitter<InternshipApprovalListState> emit,
  ) async {
    emit(InternshipApprovalListLoading());

    final params = GetInternshipApprovalListParams(
      planId: event.planId,
      type: event.type,
    );

    final result = await getInternshipApprovalListUseCase(params);

    result.fold(
      (failure) {
        emit(InternshipApprovalListError(
          message: failure.message,
          type: event.type,
        ));
      },
      (applications) {
        emit(InternshipApprovalListLoaded(
          applications: applications,
          type: event.type,
        ));
      },
    );
  }

  /// 处理刷新实习申请审批列表事件
  Future<void> _onRefreshInternshipApprovalList(
    RefreshInternshipApprovalListEvent event,
    Emitter<InternshipApprovalListState> emit,
  ) async {
    // 如果当前状态是已加载状态，则显示刷新状态
    if (state is InternshipApprovalListLoaded) {
      final currentState = state as InternshipApprovalListLoaded;
      emit(InternshipApprovalListRefreshing(
        applications: currentState.applications,
        type: event.type,
      ));
    } else {
      emit(InternshipApprovalListLoading());
    }

    final params = GetInternshipApprovalListParams(
      planId: event.planId,
      type: event.type,
    );

    final result = await getInternshipApprovalListUseCase(params);

    result.fold(
      (failure) {
        emit(InternshipApprovalListError(
          message: failure.message,
          type: event.type,
        ));
      },
      (applications) {
        emit(InternshipApprovalListLoaded(
          applications: applications,
          type: event.type,
        ));
      },
    );
  }
}
