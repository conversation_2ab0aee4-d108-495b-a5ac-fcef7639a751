/// -----
/// get_internship_approval_detail_usecase.dart
///
/// 获取实习审批详情用例
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/error/failures/validation_failure.dart';
import '../../../../core/usecases/usecase.dart';
import '../../data/models/internship_approval_detail_model.dart';
import '../repositories/internship_approval_detail_repository.dart';

/// 获取实习审批详情用例参数
class GetInternshipApprovalDetailParams extends Equatable {
  /// 实习申请ID
  final String id;

  /// 构造函数
  const GetInternshipApprovalDetailParams({
    required this.id,
  });

  @override
  List<Object> get props => [id];
}

/// 获取实习审批详情用例
///
/// 封装获取实习申请详情的业务逻辑，包括数据验证和处理
class GetInternshipApprovalDetailUseCase implements UseCase<InternshipApprovalDetailModel, GetInternshipApprovalDetailParams> {
  /// 实习审批详情仓库
  final InternshipApprovalDetailRepository repository;

  /// 构造函数
  ///
  /// 参数:
  ///   - [repository]: 实习审批详情仓库实例
  const GetInternshipApprovalDetailUseCase({
    required this.repository,
  });

  @override
  Future<Either<Failure, InternshipApprovalDetailModel>> call(GetInternshipApprovalDetailParams params) async {
    // 验证参数
    if (params.id.isEmpty) {
      return const Left(ValidationFailure('实习申请ID不能为空'));
    }

    // 调用仓库获取数据
    return await repository.getInternshipApprovalDetail(params.id);
  }
}
