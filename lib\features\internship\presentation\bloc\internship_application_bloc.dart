/// -----
/// internship_application_bloc.dart
///
/// 实习申请页面BLoC
///
/// <AUTHOR>
/// @date 2025-06-24
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/features/internship/data/models/company_info.dart';
import 'package:flutter_demo/features/internship/data/models/position_info.dart';
import 'package:flutter_demo/features/internship/data/models/internship_info.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/internship_application_event.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/internship_application_state.dart';
import 'package:flutter_demo/features/internship/presentation/utils/form_validators.dart';
import 'package:flutter_demo/features/internship/domain/usecases/submit_internship_application_usecase.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_state.dart';
import 'package:flutter_demo/core/storage/local_storage.dart';
import 'package:intl/intl.dart';

/// 实习申请BLoC
class InternshipApplicationBloc extends Bloc<InternshipApplicationEvent, InternshipApplicationState> {
  /// 提交实习申请用例
  final SubmitInternshipApplicationUseCase submitInternshipApplicationUseCase;

  /// 全局实习计划BLoC
  final PlanListGlobalBloc planListGlobalBloc;

  /// 本地存储
  final LocalStorage localStorage;

  InternshipApplicationBloc({
    required this.submitInternshipApplicationUseCase,
    required this.planListGlobalBloc,
    required this.localStorage,
  }) : super(InternshipApplicationState.initial()) {
    on<InitializeApplicationEvent>(_onInitializeApplication);
    on<UpdateCompanyInfoEvent>(_onUpdateCompanyInfo);
    on<UpdatePositionInfoEvent>(_onUpdatePositionInfo);
    on<UpdateInternshipInfoEvent>(_onUpdateInternshipInfo);
    on<SelectDateEvent>(_onSelectDate);
    on<SelectLocationEvent>(_onSelectLocation);
    on<SubmitApplicationEvent>(_onSubmitApplication);
    on<ValidateFormEvent>(_onValidateForm);
  }

  /// 初始化申请
  void _onInitializeApplication(
    InitializeApplicationEvent event,
    Emitter<InternshipApplicationState> emit,
  ) {
    print('InternshipApplicationBloc: 初始化事件，planId = ${event.planId}');
    // 加载示例数据并设置planId
    emit(state.copyWith(
      planId: event.planId,
      isLoading: false,
      companyInfo: CompanyInfo.sampleData(),
      positionInfo: PositionInfo.sampleData(),
      internshipInfo: InternshipInfo.sampleData(),
    ));
    print('InternshipApplicationBloc: 初始化完成，state.planId = ${state.planId}');
  }

  /// 更新企业信息
  void _onUpdateCompanyInfo(
    UpdateCompanyInfoEvent event,
    Emitter<InternshipApplicationState> emit,
  ) {
    final updatedCompanyInfo = _updateCompanyInfoField(state.companyInfo, event.field, event.value);
    emit(state.copyWith(companyInfo: updatedCompanyInfo));
    _validateAndUpdateForm(emit);
  }

  /// 更新岗位信息
  void _onUpdatePositionInfo(
    UpdatePositionInfoEvent event,
    Emitter<InternshipApplicationState> emit,
  ) {
    final updatedPositionInfo = _updatePositionInfoField(state.positionInfo, event.field, event.value);
    emit(state.copyWith(positionInfo: updatedPositionInfo));
    _validateAndUpdateForm(emit);
  }

  /// 更新实习信息
  void _onUpdateInternshipInfo(
    UpdateInternshipInfoEvent event,
    Emitter<InternshipApplicationState> emit,
  ) {
    final updatedInternshipInfo = _updateInternshipInfoField(state.internshipInfo, event.field, event.value);
    emit(state.copyWith(internshipInfo: updatedInternshipInfo));
    _validateAndUpdateForm(emit);
  }

  /// 选择日期
  void _onSelectDate(
    SelectDateEvent event,
    Emitter<InternshipApplicationState> emit,
  ) {
    final formattedDate = DateFormat('yyyy-MM-dd').format(event.date);
    
    if (event.field == 'startDate' || event.field == 'endDate') {
      final updatedInternshipInfo = _updateInternshipInfoField(state.internshipInfo, event.field, formattedDate);
      emit(state.copyWith(internshipInfo: updatedInternshipInfo));
    }
    
    _validateAndUpdateForm(emit);
  }

  /// 选择地址
  void _onSelectLocation(
    SelectLocationEvent event,
    Emitter<InternshipApplicationState> emit,
  ) {
    if (event.field == 'companyLocation') {
      final updatedCompanyInfo = _updateCompanyInfoField(state.companyInfo, 'location', event.location);
      emit(state.copyWith(companyInfo: updatedCompanyInfo));
    } else if (event.field == 'positionLocation') {
      final updatedPositionInfo = _updatePositionInfoField(state.positionInfo, 'location', event.location);
      emit(state.copyWith(positionInfo: updatedPositionInfo));
    } else if (event.field == 'accommodationArea') {
      final updatedInternshipInfo = _updateInternshipInfoField(state.internshipInfo, 'accommodationArea', event.location);
      emit(state.copyWith(internshipInfo: updatedInternshipInfo));
    }
    
    _validateAndUpdateForm(emit);
  }

  /// 提交申请
  Future<void> _onSubmitApplication(
    SubmitApplicationEvent event,
    Emitter<InternshipApplicationState> emit,
  ) async {
    emit(state.copyWith(isSubmitting: true));

    // 先验证表单
    final allErrors = <String, String>{};
    allErrors.addAll(FormValidators.validateCompanyInfo(state.companyInfo));
    allErrors.addAll(FormValidators.validatePositionInfo(state.positionInfo));
    allErrors.addAll(FormValidators.validateInternshipInfo(state.internshipInfo));

    if (allErrors.isNotEmpty) {
      emit(state.copyWith(
        isSubmitting: false,
        validationErrors: allErrors,
        isFormValid: false,
        errorMessage: '请完善表单信息',
      ));
      return;
    }

    try {
      // 从全局状态获取当前实习计划ID
      String currentPlanId = state.planId;
      print('InternshipApplicationBloc: 初始planId = $currentPlanId');

      final globalState = planListGlobalBloc.state;
      print('InternshipApplicationBloc: 全局状态类型 = ${globalState.runtimeType}');

      if (globalState is PlanListGlobalLoadedState && globalState.currentPlanId != null) {
        currentPlanId = globalState.currentPlanId!;
        print('InternshipApplicationBloc: 从全局状态获取planId = $currentPlanId');
      }

      // 如果planId仍然为空，尝试从本地存储获取
      if (currentPlanId.isEmpty) {
        currentPlanId = localStorage.getString('current_plan_id') ?? '';
        print('InternshipApplicationBloc: 从本地存储获取planId = $currentPlanId');
      }

      print('InternshipApplicationBloc: 最终使用的planId = $currentPlanId');

      // 调用用例提交申请
      final params = SubmitInternshipApplicationParams(
        planId: currentPlanId,
        companyInfo: state.companyInfo.toJson(),
        positionInfo: state.positionInfo.toJson(),
        internshipInfo: state.internshipInfo.toJson(),
      );

      final result = await submitInternshipApplicationUseCase(params);

      result.fold(
        (failure) {
          emit(state.copyWith(
            isSubmitting: false,
            errorMessage: failure.message,
          ));
        },
        (success) {
          emit(state.copyWith(
            isSubmitting: false,
            successMessage: '提交成功',
            validationErrors: {},
            isFormValid: true,
          ));
        },
      );
    } catch (e) {
      emit(state.copyWith(
        isSubmitting: false,
        errorMessage: '提交失败: $e',
      ));
    }
  }

  /// 验证表单
  void _onValidateForm(
    ValidateFormEvent event,
    Emitter<InternshipApplicationState> emit,
  ) {
    _validateAndUpdateForm(emit);
  }

  /// 验证并更新表单状态
  void _validateAndUpdateForm(Emitter<InternshipApplicationState> emit) {
    final allErrors = <String, String>{};
    allErrors.addAll(FormValidators.validateCompanyInfo(state.companyInfo));
    allErrors.addAll(FormValidators.validatePositionInfo(state.positionInfo));
    allErrors.addAll(FormValidators.validateInternshipInfo(state.internshipInfo));
    
    emit(state.copyWith(
      validationErrors: allErrors,
      isFormValid: allErrors.isEmpty,
    ));
  }

  /// 更新企业信息字段
  CompanyInfo _updateCompanyInfoField(CompanyInfo companyInfo, String field, String value) {
    switch (field) {
      case 'name':
        return CompanyInfo(
          name: value,
          creditCode: companyInfo.creditCode,
          size: companyInfo.size,
          type: companyInfo.type,
          industry: companyInfo.industry,
          location: companyInfo.location,
          address: companyInfo.address,
          contactPerson: companyInfo.contactPerson,
          contactPhone: companyInfo.contactPhone,
          email: companyInfo.email,
          zipCode: companyInfo.zipCode,
        );
      case 'creditCode':
        return CompanyInfo(
          name: companyInfo.name,
          creditCode: value,
          size: companyInfo.size,
          type: companyInfo.type,
          industry: companyInfo.industry,
          location: companyInfo.location,
          address: companyInfo.address,
          contactPerson: companyInfo.contactPerson,
          contactPhone: companyInfo.contactPhone,
          email: companyInfo.email,
          zipCode: companyInfo.zipCode,
        );
      case 'size':
        return CompanyInfo(
          name: companyInfo.name,
          creditCode: companyInfo.creditCode,
          size: value,
          type: companyInfo.type,
          industry: companyInfo.industry,
          location: companyInfo.location,
          address: companyInfo.address,
          contactPerson: companyInfo.contactPerson,
          contactPhone: companyInfo.contactPhone,
          email: companyInfo.email,
          zipCode: companyInfo.zipCode,
        );
      case 'type':
        return CompanyInfo(
          name: companyInfo.name,
          creditCode: companyInfo.creditCode,
          size: companyInfo.size,
          type: value,
          industry: companyInfo.industry,
          location: companyInfo.location,
          address: companyInfo.address,
          contactPerson: companyInfo.contactPerson,
          contactPhone: companyInfo.contactPhone,
          email: companyInfo.email,
          zipCode: companyInfo.zipCode,
        );
      case 'industry':
        return CompanyInfo(
          name: companyInfo.name,
          creditCode: companyInfo.creditCode,
          size: companyInfo.size,
          type: companyInfo.type,
          industry: value,
          location: companyInfo.location,
          address: companyInfo.address,
          contactPerson: companyInfo.contactPerson,
          contactPhone: companyInfo.contactPhone,
          email: companyInfo.email,
          zipCode: companyInfo.zipCode,
        );
      case 'location':
        return CompanyInfo(
          name: companyInfo.name,
          creditCode: companyInfo.creditCode,
          size: companyInfo.size,
          type: companyInfo.type,
          industry: companyInfo.industry,
          location: value,
          address: companyInfo.address,
          contactPerson: companyInfo.contactPerson,
          contactPhone: companyInfo.contactPhone,
          email: companyInfo.email,
          zipCode: companyInfo.zipCode,
        );
      case 'address':
        return CompanyInfo(
          name: companyInfo.name,
          creditCode: companyInfo.creditCode,
          size: companyInfo.size,
          type: companyInfo.type,
          industry: companyInfo.industry,
          location: companyInfo.location,
          address: value,
          contactPerson: companyInfo.contactPerson,
          contactPhone: companyInfo.contactPhone,
          email: companyInfo.email,
          zipCode: companyInfo.zipCode,
        );
      case 'contactPerson':
        return CompanyInfo(
          name: companyInfo.name,
          creditCode: companyInfo.creditCode,
          size: companyInfo.size,
          type: companyInfo.type,
          industry: companyInfo.industry,
          location: companyInfo.location,
          address: companyInfo.address,
          contactPerson: value,
          contactPhone: companyInfo.contactPhone,
          email: companyInfo.email,
          zipCode: companyInfo.zipCode,
        );
      case 'contactPhone':
        return CompanyInfo(
          name: companyInfo.name,
          creditCode: companyInfo.creditCode,
          size: companyInfo.size,
          type: companyInfo.type,
          industry: companyInfo.industry,
          location: companyInfo.location,
          address: companyInfo.address,
          contactPerson: companyInfo.contactPerson,
          contactPhone: value,
          email: companyInfo.email,
          zipCode: companyInfo.zipCode,
        );
      case 'email':
        return CompanyInfo(
          name: companyInfo.name,
          creditCode: companyInfo.creditCode,
          size: companyInfo.size,
          type: companyInfo.type,
          industry: companyInfo.industry,
          location: companyInfo.location,
          address: companyInfo.address,
          contactPerson: companyInfo.contactPerson,
          contactPhone: companyInfo.contactPhone,
          email: value,
          zipCode: companyInfo.zipCode,
        );
      default:
        return companyInfo;
    }
  }

  /// 更新岗位信息字段
  PositionInfo _updatePositionInfoField(PositionInfo positionInfo, String field, String value) {
    switch (field) {
      case 'department':
        return PositionInfo(
          name: positionInfo.name,
          department: value,
          type: positionInfo.type,
          startDate: positionInfo.startDate,
          endDate: positionInfo.endDate,
          period: positionInfo.period,
          daysPerWeek: positionInfo.daysPerWeek,
          description: positionInfo.description,
          supervisor: positionInfo.supervisor,
          supervisorPhone: positionInfo.supervisorPhone,
          positionCategory: positionInfo.positionCategory,
          workContent: positionInfo.workContent,
          location: positionInfo.location,
          address: positionInfo.address,
        );
      case 'name':
        return PositionInfo(
          name: value,
          department: positionInfo.department,
          type: positionInfo.type,
          startDate: positionInfo.startDate,
          endDate: positionInfo.endDate,
          period: positionInfo.period,
          daysPerWeek: positionInfo.daysPerWeek,
          description: positionInfo.description,
          supervisor: positionInfo.supervisor,
          supervisorPhone: positionInfo.supervisorPhone,
          positionCategory: positionInfo.positionCategory,
          workContent: positionInfo.workContent,
          location: positionInfo.location,
          address: positionInfo.address,
        );
      case 'type':
        return PositionInfo(
          name: positionInfo.name,
          department: positionInfo.department,
          type: value,
          startDate: positionInfo.startDate,
          endDate: positionInfo.endDate,
          period: positionInfo.period,
          daysPerWeek: positionInfo.daysPerWeek,
          description: positionInfo.description,
          supervisor: positionInfo.supervisor,
          supervisorPhone: positionInfo.supervisorPhone,
          positionCategory: positionInfo.positionCategory,
          workContent: positionInfo.workContent,
          location: positionInfo.location,
          address: positionInfo.address,
        );
      case 'supervisor':
        return PositionInfo(
          name: positionInfo.name,
          department: positionInfo.department,
          type: positionInfo.type,
          startDate: positionInfo.startDate,
          endDate: positionInfo.endDate,
          period: positionInfo.period,
          daysPerWeek: positionInfo.daysPerWeek,
          description: positionInfo.description,
          supervisor: value,
          supervisorPhone: positionInfo.supervisorPhone,
          positionCategory: positionInfo.positionCategory,
          workContent: positionInfo.workContent,
          location: positionInfo.location,
          address: positionInfo.address,
        );
      case 'supervisorPhone':
        return PositionInfo(
          name: positionInfo.name,
          department: positionInfo.department,
          type: positionInfo.type,
          startDate: positionInfo.startDate,
          endDate: positionInfo.endDate,
          period: positionInfo.period,
          daysPerWeek: positionInfo.daysPerWeek,
          description: positionInfo.description,
          supervisor: positionInfo.supervisor,
          supervisorPhone: value,
          positionCategory: positionInfo.positionCategory,
          workContent: positionInfo.workContent,
          location: positionInfo.location,
          address: positionInfo.address,
        );
      case 'location':
        return PositionInfo(
          name: positionInfo.name,
          department: positionInfo.department,
          type: positionInfo.type,
          startDate: positionInfo.startDate,
          endDate: positionInfo.endDate,
          period: positionInfo.period,
          daysPerWeek: positionInfo.daysPerWeek,
          description: positionInfo.description,
          supervisor: positionInfo.supervisor,
          supervisorPhone: positionInfo.supervisorPhone,
          positionCategory: positionInfo.positionCategory,
          workContent: positionInfo.workContent,
          location: value,
          address: positionInfo.address,
        );
      case 'address':
        return PositionInfo(
          name: positionInfo.name,
          department: positionInfo.department,
          type: positionInfo.type,
          startDate: positionInfo.startDate,
          endDate: positionInfo.endDate,
          period: positionInfo.period,
          daysPerWeek: positionInfo.daysPerWeek,
          description: positionInfo.description,
          supervisor: positionInfo.supervisor,
          supervisorPhone: positionInfo.supervisorPhone,
          positionCategory: positionInfo.positionCategory,
          workContent: positionInfo.workContent,
          location: positionInfo.location,
          address: value,
        );
      default:
        return positionInfo;
    }
  }

  /// 更新实习信息字段
  InternshipInfo _updateInternshipInfoField(InternshipInfo internshipInfo, String field, String value) {
    switch (field) {
      case 'startDate':
        return InternshipInfo(
          startDate: value,
          endDate: internshipInfo.endDate,
          internshipType: internshipInfo.internshipType,
          professionalMatch: internshipInfo.professionalMatch,
          salary: internshipInfo.salary,
          accommodationType: internshipInfo.accommodationType,
          accommodationArea: internshipInfo.accommodationArea,
          accommodationAddress: internshipInfo.accommodationAddress,
          provideMeals: internshipInfo.provideMeals,
          specialCircumstances: internshipInfo.specialCircumstances,
        );
      case 'endDate':
        return InternshipInfo(
          startDate: internshipInfo.startDate,
          endDate: value,
          internshipType: internshipInfo.internshipType,
          professionalMatch: internshipInfo.professionalMatch,
          salary: internshipInfo.salary,
          accommodationType: internshipInfo.accommodationType,
          accommodationArea: internshipInfo.accommodationArea,
          accommodationAddress: internshipInfo.accommodationAddress,
          provideMeals: internshipInfo.provideMeals,
          specialCircumstances: internshipInfo.specialCircumstances,
        );
      case 'internshipType':
        return InternshipInfo(
          startDate: internshipInfo.startDate,
          endDate: internshipInfo.endDate,
          internshipType: value,
          professionalMatch: internshipInfo.professionalMatch,
          salary: internshipInfo.salary,
          accommodationType: internshipInfo.accommodationType,
          accommodationArea: internshipInfo.accommodationArea,
          accommodationAddress: internshipInfo.accommodationAddress,
          provideMeals: internshipInfo.provideMeals,
          specialCircumstances: internshipInfo.specialCircumstances,
        );
      case 'professionalMatch':
        return InternshipInfo(
          startDate: internshipInfo.startDate,
          endDate: internshipInfo.endDate,
          internshipType: internshipInfo.internshipType,
          professionalMatch: value,
          salary: internshipInfo.salary,
          accommodationType: internshipInfo.accommodationType,
          accommodationArea: internshipInfo.accommodationArea,
          accommodationAddress: internshipInfo.accommodationAddress,
          provideMeals: internshipInfo.provideMeals,
          specialCircumstances: internshipInfo.specialCircumstances,
        );
      case 'salary':
        return InternshipInfo(
          startDate: internshipInfo.startDate,
          endDate: internshipInfo.endDate,
          internshipType: internshipInfo.internshipType,
          professionalMatch: internshipInfo.professionalMatch,
          salary: value,
          accommodationType: internshipInfo.accommodationType,
          accommodationArea: internshipInfo.accommodationArea,
          accommodationAddress: internshipInfo.accommodationAddress,
          provideMeals: internshipInfo.provideMeals,
          specialCircumstances: internshipInfo.specialCircumstances,
        );
      case 'accommodationType':
        return InternshipInfo(
          startDate: internshipInfo.startDate,
          endDate: internshipInfo.endDate,
          internshipType: internshipInfo.internshipType,
          professionalMatch: internshipInfo.professionalMatch,
          salary: internshipInfo.salary,
          accommodationType: value,
          accommodationArea: internshipInfo.accommodationArea,
          accommodationAddress: internshipInfo.accommodationAddress,
          provideMeals: internshipInfo.provideMeals,
          specialCircumstances: internshipInfo.specialCircumstances,
        );
      case 'accommodationArea':
        return InternshipInfo(
          startDate: internshipInfo.startDate,
          endDate: internshipInfo.endDate,
          internshipType: internshipInfo.internshipType,
          professionalMatch: internshipInfo.professionalMatch,
          salary: internshipInfo.salary,
          accommodationType: internshipInfo.accommodationType,
          accommodationArea: value,
          accommodationAddress: internshipInfo.accommodationAddress,
          provideMeals: internshipInfo.provideMeals,
          specialCircumstances: internshipInfo.specialCircumstances,
        );
      case 'accommodationAddress':
        return InternshipInfo(
          startDate: internshipInfo.startDate,
          endDate: internshipInfo.endDate,
          internshipType: internshipInfo.internshipType,
          professionalMatch: internshipInfo.professionalMatch,
          salary: internshipInfo.salary,
          accommodationType: internshipInfo.accommodationType,
          accommodationArea: internshipInfo.accommodationArea,
          accommodationAddress: value,
          provideMeals: internshipInfo.provideMeals,
          specialCircumstances: internshipInfo.specialCircumstances,
        );
      case 'provideMeals':
        return InternshipInfo(
          startDate: internshipInfo.startDate,
          endDate: internshipInfo.endDate,
          internshipType: internshipInfo.internshipType,
          professionalMatch: internshipInfo.professionalMatch,
          salary: internshipInfo.salary,
          accommodationType: internshipInfo.accommodationType,
          accommodationArea: internshipInfo.accommodationArea,
          accommodationAddress: internshipInfo.accommodationAddress,
          provideMeals: value,
          specialCircumstances: internshipInfo.specialCircumstances,
        );
      case 'specialCircumstances':
        return InternshipInfo(
          startDate: internshipInfo.startDate,
          endDate: internshipInfo.endDate,
          internshipType: internshipInfo.internshipType,
          professionalMatch: internshipInfo.professionalMatch,
          salary: internshipInfo.salary,
          accommodationType: internshipInfo.accommodationType,
          accommodationArea: internshipInfo.accommodationArea,
          accommodationAddress: internshipInfo.accommodationAddress,
          provideMeals: internshipInfo.provideMeals,
          specialCircumstances: value,
        );
      default:
        return internshipInfo;
    }
  }
}
