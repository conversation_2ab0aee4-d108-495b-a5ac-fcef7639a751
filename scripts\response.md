根据实习申请ID获取实习申请详情-老师端
接口：/v1/internship/apply/teacher/detail
GET
入参：id 实习申请ID



返回结果：
```json
{
  "data": {
    "address": "string",
    "city": "string",
    "companyCode": "string",
    "companyName": "string",
    "companyTeacher": "string",
    "contactEmail": "string",
    "contactPerson": "string",
    "contactPhone": "string",
    "description": "string",
    "fileList": [
      {
        "fileCode": 0,
        "fileName": "string",
        "fileType": "string",
        "fileUrl": "string",
        "isRequired": 0
      }
    ],
    "id": 0,
    "industry": "string",
    "jobAddress": "string",
    "jobCategory": "string",
    "jobCity": "string",
    "jobDept": "string",
    "jobEndTime": 0,
    "jobFood": 0,
    "jobHouse": "string",
    "jobHouseAddress": "string",
    "jobMajor": "string",
    "jobName": "string",
    "jobProvince": "string",
    "jobSalary": "string",
    "jobSpecial": 0,
    "jobStartTime": 0,
    "jobType": "string",
    "nature": "string",
    "planId": 0,
    "province": "string",
    "scale": "string",
    "teacherPhone": "string"
  },
  "resultCode": "string",
  "resultMsg": "string"
}
```
返回参数说明：
{
address	string 详细地址
city	string 市
companyCode	string 统一社会信用代码
companyName	string 企业名称
companyTeacher	string 企业老师
contactEmail	string 企业邮箱
contactPerson	string 企业联系人
contactPhone	string 企业联系人电话
description	string 工作内容
fileList	[...] 文件列表
id	integer($int64) 实习申请ID
industry	string 所属行业
jobAddress	string 详细地址
jobCategory	string 岗位类别
jobCity	string 岗位市
jobDept	string 岗位部门
jobEndTime	integer($int64,时间戳毫秒) 岗位结束时间
jobFood	integer($int32) 是否提供伙食（0=否，1=是）
jobHouse	string 住宿类型
jobHouseAddress	string 住宿地址
jobMajor	string 专业匹配
jobName	string 岗位名称
jobProvince	string 岗位省
jobSalary	string 实习薪资
jobSpecial	integer($int32) 实习工作是否有特殊情况（0=无，1=有）
jobStartTime	integer($int64,时间戳毫秒) 岗位开始时间
jobType	string 实习方式
nature	string 企业性质（例：国企/民企/外企/事业单位…）
planId	integer($int64) 关联的实习计划ID
province	string 省份
scale	string 企业规模（例~~：<100人 / 100-500人 / 500+）~~
teacherPhone	string  企业老师电话

}