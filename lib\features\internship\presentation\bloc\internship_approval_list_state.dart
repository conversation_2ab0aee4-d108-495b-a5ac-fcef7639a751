/// -----
/// internship_approval_list_state.dart
///
/// 实习申请审批列表状态定义
/// 定义教师端实习申请审批列表页面的所有状态
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';
import 'package:flutter_demo/features/internship/data/models/internship_application_model.dart';

/// 实习申请审批列表状态基类
abstract class InternshipApprovalListState extends Equatable {
  const InternshipApprovalListState();

  @override
  List<Object> get props => [];
}

/// 初始状态
class InternshipApprovalListInitial extends InternshipApprovalListState {}

/// 加载中状态
class InternshipApprovalListLoading extends InternshipApprovalListState {}

/// 加载成功状态
class InternshipApprovalListLoaded extends InternshipApprovalListState {
  /// 实习申请列表
  final List<InternshipApplicationModel> applications;
  
  /// 申请类型（0=待审批，1=已审批）
  final int type;

  /// 构造函数
  const InternshipApprovalListLoaded({
    required this.applications,
    required this.type,
  });

  @override
  List<Object> get props => [applications, type];
}

/// 刷新中状态
class InternshipApprovalListRefreshing extends InternshipApprovalListState {
  /// 当前的实习申请列表（保持显示）
  final List<InternshipApplicationModel> applications;
  
  /// 申请类型（0=待审批，1=已审批）
  final int type;

  /// 构造函数
  const InternshipApprovalListRefreshing({
    required this.applications,
    required this.type,
  });

  @override
  List<Object> get props => [applications, type];
}

/// 加载失败状态
class InternshipApprovalListError extends InternshipApprovalListState {
  /// 错误消息
  final String message;
  
  /// 申请类型（0=待审批，1=已审批）
  final int type;

  /// 构造函数
  const InternshipApprovalListError({
    required this.message,
    required this.type,
  });

  @override
  List<Object> get props => [message, type];
}
