/// -----
/// internship_approval_detail_screen.dart
///
/// 实习审核详情页面，用于显示实习申请的详细信息和审批状态
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/loading_widget.dart';
import 'package:flutter_demo/features/internship/data/models/internship_approval_detail_model.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/approval_status_item.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/attachment_item.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/info_item.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/info_section_card.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/student_header.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/internship_approval_detail/internship_approval_detail_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/internship_approval_detail/internship_approval_detail_event.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/internship_approval_detail/internship_approval_detail_state.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class InternshipApprovalDetailScreen extends StatelessWidget {
  final String approvalId;
  final String status;

  const InternshipApprovalDetailScreen({
    Key? key,
    required this.approvalId,
    required this.status,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<InternshipApprovalDetailBloc>()
        ..add(LoadInternshipApprovalDetailEvent(id: approvalId)),
      child: BlocConsumer<InternshipApprovalDetailBloc, InternshipApprovalDetailState>(
        listener: (context, state) {
          if (state is InternshipApprovalSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.message)),
            );
            Navigator.pop(context, true);
          } else if (state is InternshipApprovalFailure) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.message)),
            );
          }
        },
        builder: (context, state) {
          return Scaffold(
            backgroundColor: Colors.grey[100],
            appBar: CustomAppBar(
              title: '实习申请详情',
              centerTitle: true,
              showBackButton: true,
              actions: [
                IconButton(
                  icon: Image.asset('assets/images/statistics_icon.png', width: 32.w, height: 32.h),
                  onPressed: () {
                    // 右侧图标点击事件
                  },
                ),
              ],
            ),
            body: _buildBody(context, state),
          );
        },
      ),
    );
  }

  Widget _buildBody(BuildContext context, InternshipApprovalDetailState state) {
    if (state is InternshipApprovalDetailLoading) {
      return const Center(child: LoadingWidget());
    } else if (state is InternshipApprovalDetailError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '加载失败',
              style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8.h),
            Text(
              state.message,
              style: TextStyle(fontSize: 14.sp, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            ElevatedButton(
              onPressed: () {
                context.read<InternshipApprovalDetailBloc>()
                  .add(LoadInternshipApprovalDetailEvent(id: approvalId));
              },
              child: const Text('重试'),
            ),
          ],
        ),
      );
    } else if (state is InternshipApprovalDetailLoaded ||
               state is InternshipApprovalProcessing ||
               state is InternshipApprovalSuccess ||
               state is InternshipApprovalFailure) {

      InternshipApprovalDetailModel detailModel;
      if (state is InternshipApprovalDetailLoaded) {
        detailModel = state.detail;
      } else if (state is InternshipApprovalProcessing) {
        detailModel = state.detail;
      } else if (state is InternshipApprovalSuccess) {
        detailModel = state.detail;
      } else {
        detailModel = (state as InternshipApprovalFailure).detail;
      }

      return Stack(
        children: [
          // 内容区域
          SingleChildScrollView(
            child: Column(
              children: [
                const SizedBox(height: 10),

                // 学生信息头部
                StudentHeader(
                  name: detailModel.studentName,
                  avatar: detailModel.studentAvatar,
                ),

                const SizedBox(height: 10),

                // 企业信息
                _buildCompanyInfo(detailModel),

                // 岗位信息
                _buildPositionInfo(detailModel),

                // 实习信息
                _buildInternshipInfo(detailModel),

                // 审批状态
                _buildApprovalStatus(detailModel),

                // 底部空间，避免按钮遮挡
                const SizedBox(height: 80),
              ],
            ),
          ),

          // 底部按钮
          _buildBottomButtons(context, detailModel, state is InternshipApprovalProcessing),
        ],
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildBottomButtons(BuildContext context, InternshipApprovalDetailModel detailModel, bool isProcessing) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: CustomAppBar(
        title: '实习申请详情',
        centerTitle: true,
        showBackButton: true,
        actions: [
          IconButton(
            icon: Image.asset('assets/images/statistics_icon.png', width: 32.w, height: 32.h),
            onPressed: () {
              // TODO: 右侧图标点击事件
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          // 内容区域
          SingleChildScrollView(
            child: Column(
              children: [
                const SizedBox(height: 10),

                // 学生信息头部
                StudentHeader(
                  name: _detailModel.studentName,
                  avatar: _detailModel.studentAvatar,
                ),

                const SizedBox(height: 10),

                // 企业信息
                _buildCompanyInfo(),

                // 岗位信息
                _buildPositionInfo(),

                // 实习信息
                _buildInternshipInfo(),

                // 审批状态
                _buildApprovalStatus(),

                // 底部空间，避免按钮遮挡
                const SizedBox(height: 80),
              ],
            ),
          ),

          // 底部按钮
          _buildBottomButtons(),
        ],
      ),
    );
  }

  // 企业信息
  Widget _buildCompanyInfo() {
    final companyInfo = _detailModel.companyInfo;

    return InfoSectionCard(
      title: '企业信息',
      icon: Image.asset('assets/images/company_icon.png', width: 30.w, height: 28.h),
      actionText: '查看企业公示',
      onActionTap: () {
        // TODO: 跳转到企业公示页面
      },
      child: Column(
        children: [
          InfoItem(
            label: '企业名称',
            value: companyInfo.name,
          ),
          InfoItem(
            label: '企业统一社会信用代码',
            value: companyInfo.creditCode,
          ),
          InfoItem(
            label: '企业规模',
            value: companyInfo.size,
          ),
          InfoItem(
            label: '用人单位',
            value: companyInfo.type,
          ),
          InfoItem(
            label: '企业所在市',
            value: companyInfo.location,
          ),
          InfoItem(
            label: '详细地址',
            value: companyInfo.address,
          ),
          InfoItem(
            label: '企业联系人',
            value: companyInfo.contactPerson,
          ),
          InfoItem(
            label: '企业联系人电话',
            value: companyInfo.contactPhone,
          ),
          InfoItem(
            label: '企业邮箱',
            value: companyInfo.email,
          ),
          InfoItem(
            label: '企业邮编',
            value: companyInfo.zipCode,
            showDivider: false,
          ),
        ],
      ),
    );
  }

  // 岗位信息
  Widget _buildPositionInfo() {
    final positionInfo = _detailModel.positionInfo;

    return InfoSectionCard(
      title: '岗位信息',
      icon: Image.asset('assets/images/job_information_icon.png', width: 25.w, height: 28.h),
      child: Column(
        children: [
          InfoItem(
            label: '部门',
            value: positionInfo.department,
          ),
          InfoItem(
            label: '岗位名称',
            value: positionInfo.position,
          ),
          InfoItem(
            label: '企业岗位联系电话',
            value: positionInfo.contactPhone,
          ),
          InfoItem(
            label: '岗位类别',
            value: positionInfo.type,
          ),
          InfoItem(
            label: '工作内容',
            value: positionInfo.content,
            showDivider: false,
          ),
          InfoItem(
            label: '岗位所在省市',
            value: positionInfo.location,
          ),
          InfoItem(
            label: '岗位详细地址',
            value: positionInfo.address,
          ),

        ],
      ),
    );
  }

  // 实习信息
  Widget _buildInternshipInfo() {
    final internshipInfo = _detailModel.internshipInfo;

    return InfoSectionCard(
      title: '实习信息',
      icon: Image.asset('assets/images/internship_information_icon.png', width: 25.w, height: 28.h),
      child: Column(
        children: [
          InfoItem(
            label: '岗位开始时间',
            value: internshipInfo.startDate,
          ),
          InfoItem(
            label: '岗位结束时间',
            value: internshipInfo.endDate,
          ),
          InfoItem(
            label: '实习方式',
            value: internshipInfo.internshipType,
          ),
          InfoItem(
            label: '专业匹配度',
            value: internshipInfo.professionalMatch,
          ),
          InfoItem(
            label: '实习薪资',
            value: internshipInfo.salary,
          ),
          InfoItem(
            label: '住宿类型',
            value: internshipInfo.accommodationType,
          ),
          InfoItem(
            label: '住宿区域',
            value: internshipInfo.accommodationArea,
          ),
          InfoItem(
            label: '住宿地址',
            value: internshipInfo.accommodationAddress,
          ),
          InfoItem(
            label: '实习工作是否有特殊情况',
            value: internshipInfo.specialCircumstances,
          ),

          // 附件列表
          ..._detailModel.attachments.map((attachment) {
            return AttachmentItem(
              name: attachment.name,
              onTap: () {
                // TODO: 查看附件
              },
            );
          }).toList(),
        ],
      ),
    );
  }

  // 审批状态
  Widget _buildApprovalStatus() {
    return InfoSectionCard(
      title: '审批状态',
      icon: Image.asset('assets/images/internship_information_icon.png', width: 25.w, height: 28.h),
      child: Column(
        children: _detailModel.approvals.map((approval) {
          return ApprovalStatusItem(
            name: approval.name,
            status: approval.status,
            date: approval.date,
            isApproved: approval.isApproved,
          );
        }).toList(),
      ),
    );
  }

  // 底部按钮
  Widget _buildBottomButtons() {
    // 根据状态显示不同的按钮
    if (_detailModel.status == '待审批') {
      // 待审批状态：显示驳回和通过按钮
      return Positioned(
        bottom: 0,
        left: 0,
        right: 0,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 5,
                offset: const Offset(0, -1),
              ),
            ],
          ),
          child: Row(
            children: [
              // 驳回按钮
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    _showRejectDialog();
                  },
                  style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: Colors.red),
                    foregroundColor: Colors.red,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('驳回'),
                ),
              ),

              const SizedBox(width: 16),

              // 通过按钮
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    _approveApplication();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('通过'),
                ),
              ),
            ],
          ),
        ),
      );
    } else if (_detailModel.status == '已驳回') {
      // 已驳回状态：不显示底部按钮
      return const SizedBox.shrink(); // 返回一个空的小部件
    } else {
      // 已通过状态：只显示驳回按钮
      return Positioned(
        bottom: 0,
        left: 0,
        right: 0,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 5,
                offset: const Offset(0, -1),
              ),
            ],
          ),
          child: OutlinedButton(
            onPressed: () {
              _showRejectDialog();
            },
            style: OutlinedButton.styleFrom(
              side: const BorderSide(color: Colors.red),
              foregroundColor: Colors.red,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('驳回'),
          ),
        ),
      );
    }
  }

  // 显示驳回对话框
  void _showRejectDialog() {
    showDialog(
      context: context,
      builder: (context) {
        String reason = '';

        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            padding: const EdgeInsets.all(20),
            width: double.infinity,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 标题
                const Text(
                  '驳回理由',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),

                // 输入框
                Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFFF9F9F9),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: TextField(
                    maxLines: 5,
                    decoration: InputDecoration(
                      hintText: '请输入驳回理由',
                      hintStyle: const TextStyle(
                        color: Colors.grey,
                        fontSize: 14,
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide.none, // 去掉边框线
                      ),
                      contentPadding: const EdgeInsets.all(12),
                    ),
                    onChanged: (value) {
                      reason = value;
                    },
                  ),
                ),
                const SizedBox(height: 20),

                // 按钮
                Row(
                  children: [
                    // 取消按钮
                    Expanded(
                      child: TextButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        style: TextButton.styleFrom(
                          backgroundColor: Colors.grey[200],
                          foregroundColor: Colors.black87,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text('取消'),
                      ),
                    ),
                    const SizedBox(width: 16),

                    // 确定按钮
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          _rejectApplication(reason);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text('确定'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // 通过申请
  void _approveApplication() {
    // TODO: 调用API通过申请
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('申请已通过')),
    );

    // 返回上一页
    Navigator.pop(context, true);
  }

  // 驳回申请
  void _rejectApplication(String reason) {
    // TODO: 调用API驳回申请
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('申请已驳回')),
    );

    // 返回上一页
    Navigator.pop(context, true);
  }
}
