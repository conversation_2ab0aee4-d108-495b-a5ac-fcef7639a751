/// -----
/// internship_approval_detail_repository_impl.dart
///
/// 实习审批详情仓库实现
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:flutter_demo/core/error/failures/failure.dart';
import 'package:flutter_demo/core/error/failures/server_failure.dart';
import 'package:flutter_demo/core/error/failures/network_failure.dart';
import 'package:flutter_demo/core/error/failures/unauthorized_failure.dart';
import 'package:flutter_demo/core/error/exceptions/server_exception.dart';
import 'package:flutter_demo/core/error/exceptions/network_exception.dart';
import 'package:flutter_demo/core/error/exceptions/auth_exception.dart';
import 'package:flutter_demo/core/network/network_info.dart';
import 'package:flutter_demo/core/utils/logger.dart';

import '../../domain/repositories/internship_approval_detail_repository.dart';
import '../../domain/entities/internship_approval_request.dart';
import '../models/internship_approval_detail_model.dart';
import '../models/internship_approval_request_model.dart';
import '../datasources/remote/internship_approval_detail_remote_data_source.dart';

/// 实习审批详情仓库实现
///
/// 实现实习申请详情和审批操作相关的数据操作
class InternshipApprovalDetailRepositoryImpl implements InternshipApprovalDetailRepository {
  final InternshipApprovalDetailRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  static const String _tag = 'InternshipApprovalDetailRepository';

  InternshipApprovalDetailRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, InternshipApprovalDetailModel>> getInternshipApprovalDetail(String id) async {
    // 检查网络连接
    if (await networkInfo.isConnected) {
      try {
        Logger.info(_tag, '开始获取实习申请详情，ID: $id');
        
        // 调用远程数据源获取数据
        final response = await remoteDataSource.getInternshipApprovalDetail(id);
        
        // 将API响应转换为UI模型
        final detailModel = InternshipApprovalDetailModel.fromApiResponse(response);
        
        Logger.info(_tag, '成功获取实习申请详情');
        return Right(detailModel);
      } on ServerException catch (e) {
        Logger.error(_tag, '服务器异常: ${e.message}');
        return Left(ServerFailure(e.message));
      } on NetworkException catch (e) {
        Logger.error(_tag, '网络异常: ${e.message}');
        return Left(NetworkFailure(e.message));
      } on AuthException catch (e) {
        Logger.error(_tag, '认证异常: ${e.message}');
        return Left(UnauthorizedFailure(e.message));
      } catch (e) {
        Logger.error(_tag, '未知异常: $e');
        return Left(ServerFailure('获取实习申请详情失败: $e'));
      }
    } else {
      Logger.warning(_tag, '网络连接不可用');
      return const Left(NetworkFailure('网络连接不可用，请检查网络设置'));
    }
  }

  @override
  Future<Either<Failure, void>> approveInternshipApplication(InternshipApprovalRequest request) async {
    try {
      Logger.info(_tag, '开始审批实习申请 - ID: ${request.id}, 状态: ${request.status}');

      // 检查网络连接
      if (!await networkInfo.isConnected) {
        Logger.warning(_tag, '网络连接不可用');
        return const Left(NetworkFailure('网络连接不可用，请检查您的网络设置'));
      }

      // 转换为数据模型
      final requestModel = InternshipApprovalRequestModel.fromEntity(request);

      // 调用远程数据源
      await remoteDataSource.approveInternshipApplication(requestModel);

      Logger.info(_tag, '审批实习申请成功');
      return const Right(null);
    } on ServerException catch (e) {
      Logger.error(_tag, '服务器异常: ${e.message}');
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      Logger.error(_tag, '网络异常: ${e.message}');
      return Left(NetworkFailure(e.message));
    } on AuthException catch (e) {
      Logger.error(_tag, '认证异常: ${e.message}');
      return Left(UnauthorizedFailure(e.message));
    } catch (e) {
      Logger.error(_tag, '未知异常: $e');
      return Left(ServerFailure('审批实习申请失败: $e'));
    }
  }
}
