/// -----
/// approve_internship_application_usecase.dart
///
/// 审批实习申请用例
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/error/failures/validation_failure.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/internship_approval_request.dart';
import '../repositories/internship_approval_detail_repository.dart';

/// 审批实习申请用例参数
class ApproveInternshipApplicationParams extends Equatable {
  /// 审批请求
  final InternshipApprovalRequest request;

  /// 构造函数
  const ApproveInternshipApplicationParams({
    required this.request,
  });

  @override
  List<Object> get props => [request];
}

/// 审批实习申请用例
///
/// 封装审批实习申请的业务逻辑，包括数据验证和处理
class ApproveInternshipApplicationUseCase implements UseCase<void, ApproveInternshipApplicationParams> {
  /// 实习审批详情仓库
  final InternshipApprovalDetailRepository repository;

  /// 构造函数
  ///
  /// 参数:
  ///   - [repository]: 实习审批详情仓库实例
  const ApproveInternshipApplicationUseCase({
    required this.repository,
  });

  @override
  Future<Either<Failure, void>> call(ApproveInternshipApplicationParams params) async {
    // 验证参数
    if (params.request.id.isEmpty) {
      return const Left(ValidationFailure('实习申请ID不能为空'));
    }

    if (params.request.reviewOpinion.isEmpty) {
      return const Left(ValidationFailure('审批意见不能为空'));
    }

    if (params.request.status != 1 && params.request.status != 2) {
      return const Left(ValidationFailure('审批状态参数无效'));
    }

    // 调用仓库执行审批
    return await repository.approveInternshipApplication(params.request);
  }
}
