/// 公司信息实体类
///
/// 用于表示和管理实习公司的详细信息，包括基本信息、联系方式和地址等。
/// 实现了JSON序列化和反序列化方法，便于网络传输和本地存储。
///
/// 示例代码：
/// ```dart
/// final company = CompanyInfo(
///   name: '示例公司',
///   creditCode: '*********',
///   // ...其他参数
/// );
/// ```
class CompanyInfo {
  /// 公司名称
  final String name;
  /// 统一社会信用代码
  final String creditCode;
  /// 公司规模（如：'100-499人'）
  final String size;
  /// 公司类型（如：'民营（私营）企业'）
  final String type;
  /// 所属行业
  final String industry;
  /// 公司所在地区（省/市/区）
  final String location;
  /// 详细地址
  final String address;
  /// 联系人姓名
  final String contactPerson;
  /// 联系电话
  final String contactPhone;
  /// 电子邮箱
  final String email;
  /// 邮政编码
  final String zipCode;

  /// 构造函数
  ///
  /// 创建一个[CompanyInfo]实例
  ///
  /// 所有参数都是必需的，用于完整描述一个公司实体。
  const CompanyInfo({
    required this.name,
    required this.creditCode,
    required this.size,
    required this.type,
    required this.industry,
    required this.location,
    required this.address,
    required this.contactPerson,
    required this.contactPhone,
    required this.email,
    required this.zipCode,
  });

  /// 从JSON Map创建[CompanyInfo]实例
  ///
  /// 用于从服务器响应或本地存储中反序列化公司信息
  ///
  /// 参数:
  ///   - [json]: 包含公司信息的JSON对象
  factory CompanyInfo.fromJson(Map<String, dynamic> json) {
    return CompanyInfo(
      name: json['name'] ?? '',
      creditCode: json['creditCode'] ?? '',
      size: json['size'] ?? '',
      type: json['type'] ?? '',
      industry: json['industry'] ?? '',
      location: json['location'] ?? '',
      address: json['address'] ?? '',
      contactPerson: json['contactPerson'] ?? '',
      contactPhone: json['contactPhone'] ?? '',
      email: json['email'] ?? '',
      zipCode: json['zipCode'] ?? '',
    );
  }

  /// 将[CompanyInfo]实例转换为JSON Map
  ///
  /// 用于将公司信息序列化为JSON格式，便于网络传输或本地存储
  ///
  /// 返回:
  ///   包含公司信息的Map对象
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'creditCode': creditCode,
      'size': size,
      'type': type,
      'industry': industry,
      'location': location,
      'address': address,
      'contactPerson': contactPerson,
      'contactPhone': contactPhone,
      'email': email,
      'zipCode': zipCode,
    };
  }

  /// 获取示例数据
  ///
  /// 用于开发和测试，返回一个预定义的[CompanyInfo]实例
  ///
  /// 返回:
  ///   包含示例数据的[CompanyInfo]实例
  static CompanyInfo sampleData() {
    return CompanyInfo(
      name: '掌淘网络科技（上海）有限公司',
      creditCode: '91310000332546552F',
      size: '100-499人',
      type: '民营（私营）企业',
      industry: '软件和信息技术服务业',
      location: '广东省/广州市/荔湾区',
      address: '缅甸',
      contactPerson: '易民',
      contactPhone: '18627171276',
      email: '<EMAIL>',
      zipCode: '',
    );
  }
} 