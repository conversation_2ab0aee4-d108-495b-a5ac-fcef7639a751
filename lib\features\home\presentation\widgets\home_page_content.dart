/// -----
/// home_page_content.dart
///
/// 首页实际内容展示组件
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/network/module_service.dart';
import 'package:flutter_demo/core/router/app_navigator.dart';
import 'package:flutter_demo/core/router/module_navigation_service.dart';
import 'package:flutter_demo/core/router/route_constants.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter_demo/core/widgets/top_banner_section.dart';
import 'package:flutter_demo/features/home/<USER>/module_item.dart';
import 'package:flutter_demo/features/home/<USER>/module_section.dart';
import 'package:flutter_demo/features/auth/data/models/role_type.dart';
import 'package:flutter_demo/features/notification/presentation/screens/notification_screen.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_event.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_state.dart';
import 'package:flutter_demo/core/storage/local_storage.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';


/// 首页内容组件
///
/// 展示首页的具体内容，包括轮播图、公告栏和功能模块
class HomePageContent extends StatefulWidget {
  const HomePageContent({
    Key? key,
  }) : super(key: key);

  @override
  State<HomePageContent> createState() => _HomePageContentState();
}

/// 首页内容状态管理类
///
/// 管理首页各个模块的状态和数据加载
class _HomePageContentState extends State<HomePageContent> {
  /// 轮播图当前页面索引
  int _currentBannerIndex = 0;

  /// 轮播图图片列表
  final List<String> _bannerImages = [
    'assets/images/banner1.jpg',
    'assets/images/banner2.jpg',
    'assets/images/banner3.jpg',
    'assets/images/banner4.jpg',
  ];

  /// 轮播图页面控制器
  final PageController _pageController = PageController();

  /// 模块服务实例
  final ModuleService _moduleService = ModuleService();

  /// 模块数据存储
  Map<String, ModuleSection> _moduleSections = {};

  /// 数据加载状态标识
  bool _isLoading = true;

  /// 用户类型
  String? _userType;

  @override
  void initState() {
    super.initState();
    _loadUserTypeAndModules();
    _loadInternshipPlanList();
  }

  /// 加载实习计划列表
  void _loadInternshipPlanList() {
    try {
      final planListBloc = GetIt.instance<PlanListGlobalBloc>();
      planListBloc.add(const LoadPlanListGlobalEvent());
    } catch (e) {
      Logger.error('HomePageContent', '加载实习计划列表失败: $e');
    }
  }

  /// 加载用户类型和模块数据
  Future<void> _loadUserTypeAndModules() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userType = prefs.getString(AppConstants.userTypeKey);

      setState(() {
        _userType = userType;
      });

      // 根据用户类型确定角色类型
      RoleType roleType = _getUserRoleType();

      // 根据角色类型加载不同的模块数据
      final modules = await _moduleService.loadModules(roleType: roleType);
      setState(() {
        _moduleSections = modules;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading modules: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 根据用户类型获取对应的角色类型
  RoleType _getUserRoleType() {
    switch (_userType) {
      case '1':
        return RoleType.student;
      case '2':
        return RoleType.teacher;
      case '3':
        return RoleType.hr;
      default:
        return RoleType.student; // 默认为学生角色
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<PlanListGlobalBloc, PlanListGlobalState>(
      bloc: GetIt.instance<PlanListGlobalBloc>(),
      listener: (context, state) {
        // 当实习计划加载成功时，保存当前选中的计划ID到本地存储
        if (state is PlanListGlobalLoadedState && state.currentPlan != null) {
          final localStorage = GetIt.instance<LocalStorage>();
          localStorage.setString('current_plan_id', state.currentPlan!.planId);
          print('HomePageContent: 保存planId到本地存储: ${state.currentPlan!.planId}');
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F7FA),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 根据用户类型决定顶部区域的显示方式
                  if (_userType == '1') ...[
                    // 学生端保持原样，添加顶部安全区域
                    SizedBox(height: MediaQuery.of(context).padding.top),
                    _buildBannerSlider(),
                    _buildNoticeBar(),
                  ] else if (_userType == '2' || _userType == '3') ...[
                    // 教师端和企业端不添加顶部安全区域，让图片延伸到状态栏
                    _buildTeacherOrHrTopSection(context),
                  ] else ...[
                    // 其他角色保持原样
                    SizedBox(height: MediaQuery.of(context).padding.top),
                    _buildBannerSlider(),
                    _buildNoticeBar(),
                  ],
                  // 动态加载各个功能模块
                  ..._moduleSections.entries.map((entry) => _buildModuleSection(entry.value)).toList(),
                  const SizedBox(height: 90), // 增加底部空间以避免被底部导航栏遮挡
                ],
              ),
            ),
      ),
    );
  }

  /// 教师或HR角色的顶部区域（包含顶部图片和消息通知）
  Widget _buildTeacherOrHrTopSection(BuildContext context) {
    return TopBannerSection(
      backgroundImagePath: 'assets/images/home_teacher_top_banner.png',
      rightIconPath: '',
      welcomeTitle: '',
      welcomeSubtitle: '',
      welcomeDescription: _getWelcomeSubtitle(),
      noticeText: _getNoticeText(),
      onNoticeTap: () {
        // 处理通知栏点击事件
        context.push(AppRoutes.notice);
      },
    );
  }

  /// 构建模块部分
  ///
  /// [section] 模块配置信息
  Widget _buildModuleSection(ModuleSection section) {

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 16, top: 12, bottom: 2),
            child: Text(
              section.title,
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
          ),
          // 使用LayoutBuilder和Row组合实现每行4个均匀分布的布局
          Padding(
            padding: const EdgeInsets.only(left: 8, right: 8, top: 2, bottom: 12),
            child: LayoutBuilder(
              builder: (context, constraints) {
                // 计算每行应该显示的图标数量
                const int itemsPerRow = 4; // 每行4个
                final double itemWidth = constraints.maxWidth / itemsPerRow;

                // 计算需要的行数
                final int rowCount = (section.items.length / itemsPerRow).ceil();

                // 创建行列布局
                return Column(
                  children: List.generate(rowCount, (rowIndex) {
                    // 计算当前行的起始和结束索引
                    final int startIndex = rowIndex * itemsPerRow;
                    final int endIndex = (startIndex + itemsPerRow) > section.items.length
                        ? section.items.length
                        : startIndex + itemsPerRow;

                    // 获取当前行的项目
                    final List<ModuleItem> rowItems = section.items.sublist(startIndex, endIndex);

                    // 创建当前行
                    return Row(
                      children: rowItems.map((item) {
                        return SizedBox(
                          width: itemWidth,
                          child: _buildFunctionItem(item: item),
                        );
                      }).toList(),
                    );
                  }),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 顶部区域构建方法
  /// 根据用户类型返回不同的顶部区域组件
  Widget _buildBannerSlider() {
    switch (_userType) {
      case '1': // 学生
        return _buildStudentBanner();
      case '2': // 教师
      case '3': // HR
        return Container();
      default:
        return _buildStudentBanner();
    }
  }

  /// 学生端轮播图
  Widget _buildStudentBanner() {
    return Container(
      height: 180,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            PageView.builder(
              controller: _pageController,
              itemCount: _bannerImages.length,
              onPageChanged: (index) {
                setState(() {
                  _currentBannerIndex = index;
                });
              },
              itemBuilder: (context, index) {
                if (index == 0) {
                  return Container(
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [Color(0xFF4169E1), Color(0xFF7B68EE)],
                      ),
                    ),
                    child: Stack(
                      children: [
                        Positioned(
                          right: 10,
                          bottom: 20,
                          child: Image.asset(
                            'assets/images/laptop_illustration.png',
                            width: 120,
                            height: 120,
                            errorBuilder: (context, error, stackTrace) {
                              return const Icon(
                                Icons.laptop_mac,
                                color: Colors.white,
                                size: 80,
                              );
                            },
                          ),
                        ),
                        Positioned(
                          left: 20,
                          top: 30,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                '亲爱的，你好~',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                '欢迎使用AI实习管理平台~',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 20,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(height: 16),
                              Text(
                                _getWelcomeSubtitle(),
                                style: const TextStyle(
                                  color: Colors.white70,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                } else {
                  return Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          AppTheme.primaryColor.withOpacity(0.8),
                          AppTheme.primaryColor,
                        ],
                      ),
                    ),
                    child: Center(
                      child: Image.asset(
                        _bannerImages[index],
                        fit: BoxFit.cover,
                        width: double.infinity,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: AppTheme.primaryColor,
                            child: const Center(
                              child: Icon(
                                Icons.image_not_supported_outlined,
                                color: Colors.white,
                                size: 50,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  );
                }
              },
            ),
            Positioned(
              bottom: 10,
              left: 0,
              right: 0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  _bannerImages.length,
                      (index) => Container(
                    width: _currentBannerIndex == index ? 12 : 6,
                    height: 4,
                    margin: const EdgeInsets.symmetric(horizontal: 3),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(2),
                      color: _currentBannerIndex == index ? Colors.white : Colors.white.withOpacity(0.5),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 教师端顶部图片 (此函数在此文件中可能不再直接由_buildBannerSlider调用，但保留以备其他用途或参考)
  Widget _buildTeacherBanner() {
    return Container(
      height: 180,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Image.asset(
          'assets/images/home_teacher_top_banner.png',
          fit: BoxFit.cover,
          width: double.infinity,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Color(0xFF4169E1), Color(0xFF7B68EE)],
                ),
              ),
              child: Stack(
                children: [
                  Positioned(
                    right: 10,
                    bottom: 20,
                    child: Icon(
                      Icons.laptop_mac,
                      color: Colors.white,
                      size: 80,
                    ),
                  ),
                  Positioned(
                    left: 20,
                    top: 30,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '亲爱的，你好~',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          '欢迎使用AI实习管理平台~',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _getWelcomeSubtitle(),
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

    /// 企业端顶部图片 (此函数在此文件中可能不再直接由_buildBannerSlider调用，但保留以备其他用途或参考)
  Widget _buildEnterpriseBanner() {
    return Container(
      height: 180,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Image.asset(
          'assets/images/home_teacher_top_banner.png', // 使用与教师端相同的图片
          fit: BoxFit.cover,
          width: double.infinity,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              decoration: const BoxDecoration(
                color: Color(0xFF2E5BEA),
              ),
              child: Stack(
                children: [
                  Positioned(
                    right: 20,
                    bottom: 20,
                    child: Icon(
                      Icons.business,
                      color: Colors.white,
                      size: 80,
                    ),
                  ),
                  Positioned(
                    left: 20,
                    top: 30,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '考研择校',
                          style: TextStyle(
                            color: Colors.yellow,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          '快速查询工具',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.white.withOpacity(0.5)),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Text(
                            '选择性比努力更重要',
                            style: TextStyle(
                              color: Colors.white70,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  /// 公告栏
  Widget _buildNoticeBar() {
    String noticeText = _getNoticeText();

    return GestureDetector(
      onTap: () {
        context.push(AppRoutes.notice);
      },
      child: Container(
        height: 50,
        margin: EdgeInsets.symmetric(
          horizontal: 16,
          vertical: _userType == '1' ? 8 : 0,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: _userType == '1'
              ? BorderRadius.circular(8)
              : const BorderRadius.only(
                  bottomLeft: Radius.circular(8),
                  bottomRight: Radius.circular(8),
                ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            const SizedBox(width: 16),
            Image.asset('assets/images/home_notification_icon.png',
            width: 142.w,height: 26.h,),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                noticeText,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(
                  color: Colors.black87,
                  fontSize: 14,
                ),
              ),
            ),
            const SizedBox(width: 8),
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey,
            ),
            const SizedBox(width: 16),
          ],
        ),
      ),
    );
  }

  /// 根据用户类型获取公告内容
  String _getNoticeText() {
    switch (_userType) {
      case '1':
        return 'AI实习管理系统正式上线';
      case '2':
        return '暂无公告~';
      case '3':
        return '暂无公告~';
      default:
        return 'AI实习管理系统正式上线';
    }
  }

  /// 处理功能项点击事件
  void _handleFunctionItemTap(ModuleItem item) {
    // 添加调试日志
    Logger.error('_handleFunctionItemTap','点击功能项: ${item.title}, 路由: ${item.route}, 用户类型: $_userType');

    // 使用ModuleNavigationService处理导航
    ModuleNavigationService.navigateToModule(context, item, _userType);
  }

  /// 构建功能项
  Widget _buildFunctionItem({
    required ModuleItem item,
  }) {
    // 使用 InkWell 提供点击反馈
    return InkWell(
      onTap: () {
        debugPrint('Button onPressed: ${item.title}');
        _handleFunctionItemTap(item);
      },
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Column(
          mainAxisSize: MainAxisSize.min, // 使列高度适应内容
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 图标部分
            SizedBox(
              height: 45,
              child: Stack(
                alignment: Alignment.center,
                clipBehavior: Clip.none,
                children: [
                  // 图标
                  if (item.isImagePath)
                    Image.asset(
                      item.icon,
                      width: 55.w,
                      height: 55.h,
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) {
                        debugPrint('Image load error for ${item.title}: $error');
                        return const Icon(
                          Icons.image_not_supported_outlined,
                          size: 32,
                        );
                      },
                    )
                  else
                    Icon(
                      item.getIconData(),
                      size: 32,
                      color: AppTheme.primaryColor,
                    ),

                  // 徽章
                  if (item.badgeCount > 0)
                    Positioned(
                      right: -5,
                      top: -5,
                      child: Container(
                        width: 14,
                        height: 14,
                        alignment: Alignment.center,
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                        child: Text(
                          item.badgeCount.toString(),
                          style: const TextStyle(color: Colors.white, fontSize: 8, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                ],
              ),
            ),

            const SizedBox(height: 4),

            // 标题
            Text(
              item.title,
              style: TextStyle(fontSize: 24.sp, fontWeight: FontWeight.w500, color: AppTheme.black333),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),

            // 计数
            if (item.count != null)
              Padding(
                padding: const EdgeInsets.only(top: 2),
                child: Text(
                  item.count!,
                  style: TextStyle(
                    fontSize: 22.sp,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// 根据用户类型获取欢迎语副标题
  String _getWelcomeSubtitle() {
    switch (_userType) {
      case '1':
        return '开启你的实习之旅';
      case '2':
        return '管理学生实习更轻松';
      case '3':
        return '欢迎使用AI实习管理平台~';
      default:
        return '智能实习管理系统';
    }
  }
}