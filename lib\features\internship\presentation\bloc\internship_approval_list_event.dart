/// -----
/// internship_approval_list_event.dart
///
/// 实习申请审批列表事件定义
/// 定义教师端实习申请审批列表页面的所有事件
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 实习申请审批列表事件基类
abstract class InternshipApprovalListEvent extends Equatable {
  const InternshipApprovalListEvent();

  @override
  List<Object> get props => [];
}

/// 加载实习申请审批列表事件
class LoadInternshipApprovalListEvent extends InternshipApprovalListEvent {
  /// 实习计划ID
  final String planId;
  
  /// 申请类型（0=待审批，1=已审批）
  final int type;

  /// 构造函数
  const LoadInternshipApprovalListEvent({
    required this.planId,
    required this.type,
  });

  @override
  List<Object> get props => [planId, type];
}

/// 刷新实习申请审批列表事件
class RefreshInternshipApprovalListEvent extends InternshipApprovalListEvent {
  /// 实习计划ID
  final String planId;
  
  /// 申请类型（0=待审批，1=已审批）
  final int type;

  /// 构造函数
  const RefreshInternshipApprovalListEvent({
    required this.planId,
    required this.type,
  });

  @override
  List<Object> get props => [planId, type];
}
