/// -----
/// internship_approval_request.dart
///
/// 实习申请审批请求实体类
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 实习申请审批请求实体类
/// 
/// 表示审批实习申请的请求数据
class InternshipApprovalRequest extends Equatable {
  /// 实习申请记录ID
  final String id;
  
  /// 审批意见
  final String reviewOpinion;
  
  /// 状态（1=已通过，2=驳回）
  final int status;

  const InternshipApprovalRequest({
    required this.id,
    required this.reviewOpinion,
    required this.status,
  });

  /// 创建通过审批请求
  factory InternshipApprovalRequest.approve({
    required String id,
    String reviewOpinion = '审批通过',
  }) {
    return InternshipApprovalRequest(
      id: id,
      reviewOpinion: reviewOpinion,
      status: 1, // 已通过
    );
  }

  /// 创建驳回审批请求
  factory InternshipApprovalRequest.reject({
    required String id,
    String reviewOpinion = '审批驳回',
  }) {
    return InternshipApprovalRequest(
      id: id,
      reviewOpinion: reviewOpinion,
      status: 2, // 驳回
    );
  }

  @override
  List<Object> get props => [id, reviewOpinion, status];

  @override
  String toString() {
    return 'InternshipApprovalRequest{id: $id, reviewOpinion: $reviewOpinion, status: $status}';
  }
}
