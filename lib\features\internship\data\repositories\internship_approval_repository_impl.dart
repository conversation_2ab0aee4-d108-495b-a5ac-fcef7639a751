/// -----
/// internship_approval_repository_impl.dart
///
/// 实习申请审批仓库具体实现
/// 实现教师端实习申请审批相关的数据操作
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:flutter_demo/core/error/exceptions/auth_exception.dart';
import 'package:flutter_demo/core/error/exceptions/network_exception.dart';
import 'package:flutter_demo/core/error/exceptions/server_exception.dart';
import 'package:flutter_demo/core/error/failures/auth_failure.dart';
import 'package:flutter_demo/core/error/failures/failure.dart';
import 'package:flutter_demo/core/network/network_info.dart';
import 'package:flutter_demo/features/internship/data/datasources/remote/internship_approval_remote_data_source.dart';
import 'package:flutter_demo/features/internship/data/models/internship_application_model.dart';
import 'package:flutter_demo/features/internship/domain/repositories/internship_approval_repository.dart';

/// 实习申请审批仓库具体实现
///
/// 实现教师端实习申请审批相关的数据操作，包括网络检查和异常处理
class InternshipApprovalRepositoryImpl implements InternshipApprovalRepository {
  /// 构造函数
  ///
  /// 参数:
  ///   - [remoteDataSource]: 远程数据源实例
  ///   - [networkInfo]: 网络信息实例
  const InternshipApprovalRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  /// 远程数据源
  final InternshipApprovalRemoteDataSource remoteDataSource;

  /// 网络信息
  final NetworkInfo networkInfo;

  @override
  Future<Either<Failure, List<InternshipApplicationModel>>> getInternshipApprovalList({
    required String planId,
    required int type,
  }) async {
    // 检查网络连接
    if (await networkInfo.isConnected) {
      try {
        // 调用远程数据源获取数据
        final response = await remoteDataSource.getInternshipApprovalList(
          planId: planId,
          type: type,
        );
        
        // 检查响应是否成功
        if (response.isSuccess) {
          // 将API响应数据转换为UI模型
          final status = InternshipApplicationModel.getStatusByType(type);
          final applications = response.data
              .map((item) => item.toInternshipApplicationModel(status: status))
              .toList();
          
          return Right(applications);
        } else {
          return Left(ServerFailure(response.errorMessage ?? '获取申请列表失败'));
        }
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(e.message));
      } on UnauthorizedException catch (e) {
        return Left(UnauthorizedFailure(e.message));
      } on ForbiddenException catch (e) {
        return Left(ForbiddenFailure(e.message));
      } on Exception catch (e) {
        return Left(ServerFailure('获取实习申请列表失败: $e'));
      }
    } else {
      return const Left(NetworkFailure('网络连接不可用，请检查网络设置'));
    }
  }
}
