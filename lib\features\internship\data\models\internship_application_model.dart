/// -----
/// internship_application_model.dart
///
/// 实习申请数据模型，用于存储实习申请的数据
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

class InternshipApplicationModel {
  /// 申请ID
  final String id;

  /// 学生姓名
  final String studentName;

  /// 学生头像
  final String studentAvatar;

  /// 企业名称
  final String companyName;

  /// 岗位部门
  final String department;

  /// 岗位名称
  final String position;

  /// 申请时间
  final String applyDate;

  /// 申请状态（待审批、已通过、已驳回）
  final String status;

  const InternshipApplicationModel({
    required this.id,
    required this.studentName,
    required this.studentAvatar,
    required this.companyName,
    required this.department,
    required this.position,
    required this.applyDate,
    required this.status,
  });

  /// 从JSON创建模型
  factory InternshipApplicationModel.fromJson(Map<String, dynamic> json) {
    return InternshipApplicationModel(
      id: json['id']?.toString() ?? '',
      studentName: json['studentName'] ?? '',
      studentAvatar: json['studentAvatar'] ?? '',
      companyName: json['companyName'] ?? '',
      department: json['department'] ?? '',
      position: json['position'] ?? '',
      applyDate: json['applyDate'] ?? '',
      status: json['status'] ?? '',
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'studentName': studentName,
      'studentAvatar': studentAvatar,
      'companyName': companyName,
      'department': department,
      'position': position,
      'applyDate': applyDate,
      'status': status,
    };
  }

  /// 状态常量
  static const String statusPending = '待审批';
  static const String statusApproved = '已通过';
  static const String statusRejected = '已驳回';

  /// 根据type参数获取状态文本
  static String getStatusByType(int type) {
    switch (type) {
      case 0:
        return statusPending;
      case 1:
        return statusApproved;
      default:
        return statusPending;
    }
  }
}
