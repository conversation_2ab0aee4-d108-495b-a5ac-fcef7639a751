/// -----
/// internship_approval_detail_state.dart
///
/// 实习审批详情状态定义
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';
import '../../../data/models/internship_approval_detail_model.dart';

/// 实习审批详情状态基类
abstract class InternshipApprovalDetailState extends Equatable {
  const InternshipApprovalDetailState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
class InternshipApprovalDetailInitial extends InternshipApprovalDetailState {}

/// 加载中状态
class InternshipApprovalDetailLoading extends InternshipApprovalDetailState {}

/// 加载成功状态
class InternshipApprovalDetailLoaded extends InternshipApprovalDetailState {
  /// 实习审批详情数据
  final InternshipApprovalDetailModel detail;

  const InternshipApprovalDetailLoaded({
    required this.detail,
  });

  @override
  List<Object> get props => [detail];
}

/// 加载失败状态
class InternshipApprovalDetailError extends InternshipApprovalDetailState {
  /// 错误信息
  final String message;

  const InternshipApprovalDetailError({
    required this.message,
  });

  @override
  List<Object> get props => [message];
}

/// 审批处理中状态
class InternshipApprovalProcessing extends InternshipApprovalDetailState {
  /// 当前详情数据
  final InternshipApprovalDetailModel detail;

  const InternshipApprovalProcessing({
    required this.detail,
  });

  @override
  List<Object> get props => [detail];
}

/// 审批成功状态
class InternshipApprovalSuccess extends InternshipApprovalDetailState {
  /// 更新后的详情数据
  final InternshipApprovalDetailModel detail;
  
  /// 成功消息
  final String message;

  const InternshipApprovalSuccess({
    required this.detail,
    required this.message,
  });

  @override
  List<Object> get props => [detail, message];
}

/// 审批失败状态
class InternshipApprovalFailure extends InternshipApprovalDetailState {
  /// 当前详情数据
  final InternshipApprovalDetailModel detail;
  
  /// 错误信息
  final String message;

  const InternshipApprovalFailure({
    required this.detail,
    required this.message,
  });

  @override
  List<Object> get props => [detail, message];
}
