/// -----
/// internship_approval_list_screen.dart
///
/// 老师端实习申请审批列表页面，展示待审批和已审批的实习申请
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/approval_tab_bar.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/features/internship/data/models/internship_application_model.dart';
import 'package:flutter_demo/features/internship/presentation/screens/internship_approval_detail_screen.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/internship_approval_list_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/internship_approval_list_event.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/internship_approval_list_state.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_state.dart';
import 'package:flutter_svg/flutter_svg.dart';

class InternshipApprovalListScreen extends StatefulWidget {
  const InternshipApprovalListScreen({Key? key}) : super(key: key);

  @override
  State<InternshipApprovalListScreen> createState() =>
      _InternshipApprovalListScreenState();
}

class _InternshipApprovalListScreenState
    extends State<InternshipApprovalListScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // BLoC实例
  late InternshipApprovalListBloc _pendingBloc;
  late InternshipApprovalListBloc _approvedBloc;
  late PlanListGlobalBloc _planListGlobalBloc;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // 初始化BLoC
    _pendingBloc = GetIt.instance<InternshipApprovalListBloc>();
    _approvedBloc = GetIt.instance<InternshipApprovalListBloc>();
    _planListGlobalBloc = GetIt.instance<PlanListGlobalBloc>();

    // 监听全局计划状态变化
    _planListGlobalBloc.stream.listen((state) {
      if (state is PlanListGlobalLoadedState && state.currentPlan != null) {
        _loadData(state.currentPlan!.planId);
      }
    });

    // 如果全局状态已经有当前计划，直接加载数据
    final currentState = _planListGlobalBloc.state;
    if (currentState is PlanListGlobalLoadedState && currentState.currentPlan != null) {
      _loadData(currentState.currentPlan!.planId);
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _pendingBloc.close();
    _approvedBloc.close();
    super.dispose();
  }

  /// 加载数据
  void _loadData(String planId) {
    if (planId.isNotEmpty) {
      // 加载待审批数据
      _pendingBloc.add(LoadInternshipApprovalListEvent(
        planId: planId,
        type: 0, // 待审批
      ));

      // 加载已审批数据
      _approvedBloc.add(LoadInternshipApprovalListEvent(
        planId: planId,
        type: 1, // 已审批
      ));
    }
  }

  /// 刷新数据
  void _refreshData(String planId) {
    if (planId.isNotEmpty) {
      // 刷新待审批数据
      _pendingBloc.add(RefreshInternshipApprovalListEvent(
        planId: planId,
        type: 0, // 待审批
      ));

      // 刷新已审批数据
      _approvedBloc.add(RefreshInternshipApprovalListEvent(
        planId: planId,
        type: 1, // 已审批
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<PlanListGlobalBloc, PlanListGlobalState>(
      bloc: _planListGlobalBloc,
      listener: (context, state) {
        if (state is PlanListGlobalLoadedState && state.currentPlan != null) {
          _refreshData(state.currentPlan!.planId);
        }
      },
      child: Scaffold(
        backgroundColor: Colors.grey[100],
        appBar: const CustomAppBar(
          title: '实习申请',
          centerTitle: true,
          showBackButton: true,
        ),
        body: Column(
          children: [
            // 课程头部 - 自动从全局状态获取实习计划数据
            const CourseHeaderSection(),

            // 标签栏
            BlocBuilder<InternshipApprovalListBloc, InternshipApprovalListState>(
              bloc: _pendingBloc,
              builder: (context, state) {
                int pendingCount = 0;
                if (state is InternshipApprovalListLoaded) {
                  pendingCount = state.applications.length;
                } else if (state is InternshipApprovalListRefreshing) {
                  pendingCount = state.applications.length;
                }

                return ApprovalTabBar(
                  controller: _tabController,
                  pendingCount: pendingCount,
                );
              },
            ),

            // 页面内容
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildPendingList(),
                  _buildApprovedList(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 待审批列表
  Widget _buildPendingList() {
    return BlocBuilder<InternshipApprovalListBloc, InternshipApprovalListState>(
      bloc: _pendingBloc,
      builder: (context, state) {
        if (state is InternshipApprovalListLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is InternshipApprovalListError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  state.message,
                  style: const TextStyle(color: Colors.red),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    final currentState = _planListGlobalBloc.state;
                    if (currentState is PlanListGlobalLoadedState &&
                        currentState.currentPlan != null) {
                      _loadData(currentState.currentPlan!.planId);
                    }
                  },
                  child: const Text('重试'),
                ),
              ],
            ),
          );
        } else if (state is InternshipApprovalListLoaded ||
                   state is InternshipApprovalListRefreshing) {
          final applications = state is InternshipApprovalListLoaded
              ? state.applications
              : (state as InternshipApprovalListRefreshing).applications;

          if (applications.isEmpty) {
            return const Center(
              child: Text('暂无待审批申请'),
            );
          }

          return RefreshIndicator(
            onRefresh: () async {
              final currentState = _planListGlobalBloc.state;
              if (currentState is PlanListGlobalLoadedState &&
                  currentState.currentPlan != null) {
                _refreshData(currentState.currentPlan!.planId);
              }
            },
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              itemCount: applications.length,
              itemBuilder: (context, index) {
                final item = applications[index];
                return _buildApplicationItem(item);
              },
            ),
          );
        }

        return const Center(child: Text('暂无数据'));
      },
    );
  }

  // 已审批列表
  Widget _buildApprovedList() {
    return BlocBuilder<InternshipApprovalListBloc, InternshipApprovalListState>(
      bloc: _approvedBloc,
      builder: (context, state) {
        if (state is InternshipApprovalListLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is InternshipApprovalListError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  state.message,
                  style: const TextStyle(color: Colors.red),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    final currentState = _planListGlobalBloc.state;
                    if (currentState is PlanListGlobalLoadedState &&
                        currentState.currentPlan != null) {
                      _loadData(currentState.currentPlan!.planId);
                    }
                  },
                  child: const Text('重试'),
                ),
              ],
            ),
          );
        } else if (state is InternshipApprovalListLoaded ||
                   state is InternshipApprovalListRefreshing) {
          final applications = state is InternshipApprovalListLoaded
              ? state.applications
              : (state as InternshipApprovalListRefreshing).applications;

          if (applications.isEmpty) {
            return const Center(
              child: Text('暂无已审批申请'),
            );
          }

          return RefreshIndicator(
            onRefresh: () async {
              final currentState = _planListGlobalBloc.state;
              if (currentState is PlanListGlobalLoadedState &&
                  currentState.currentPlan != null) {
                _refreshData(currentState.currentPlan!.planId);
              }
            },
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              itemCount: applications.length,
              itemBuilder: (context, index) {
                final item = applications[index];
                return _buildApplicationItem(item);
              },
            ),
          );
        }

        return const Center(child: Text('暂无数据'));
      },
    );
  }

  // 申请项
  Widget _buildApplicationItem(InternshipApplicationModel item) {
    final status = item.status;
    final statusColor = _getStatusColor(status);

    return InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => InternshipApprovalDetailScreen(
                approvalId: item.id,
                status: item.status,
              ),
            ),
          ).then((result) {
            // 如果返回结果为true，表示审批状态有变化，需要刷新列表
            if (result == true) {
              final currentState = _planListGlobalBloc.state;
              if (currentState is PlanListGlobalLoadedState &&
                  currentState.currentPlan != null) {
                _refreshData(currentState.currentPlan!.planId);
              }
            }
          });
        },
        child: Container(
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 学生信息行
                Row(
                  children: [
                    // 头像 - 使用SVG作为默认头像
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.grey[300],
                      ),
                      child: ClipOval(
                        child: item.studentAvatar.isNotEmpty
                            ? Image.network(
                                item.studentAvatar,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) =>
                                    SvgPicture.asset(
                                  'assets/images/default_avatar.svg',
                                  fit: BoxFit.cover,
                                ),
                              )
                            : SvgPicture.asset(
                                'assets/images/default_avatar.svg',
                                fit: BoxFit.cover,
                              ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // 学生姓名
                    Text(
                      item.studentName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    // 状态标签
                    Text(
                      status,
                      style: TextStyle(
                        fontSize: 14,
                        color: statusColor,
                      ),
                    ),
                  ],
                ),

                // 添加分割线
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 12),
                  child: Divider(
                      height: 1, thickness: 0.5, color: Color(0xFFEEEEEE)),
                ),

                // 实习单位
                _buildInfoRow('实习单位', item.companyName),

                const SizedBox(height: 8),

                // 部门/科室
                _buildInfoRow('部门/科室', item.department),

                const SizedBox(height: 8),

                // 实习岗位
                _buildInfoRow('实习岗位', item.position),

                const SizedBox(height: 16),

                // 申请时间和查看按钮
                Row(
                  children: [
                    Text(
                      '申请时间：${item.applyDate}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                    const Spacer(),
                    // 查看按钮
                    InkWell(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                InternshipApprovalDetailScreen(
                              approvalId: item.id,
                              status: item.status,
                            ),
                          ),
                        ).then((result) {
                          // 如果返回结果为true，表示审批状态有变化，需要刷新列表
                          if (result == true) {
                            final currentState = _planListGlobalBloc.state;
                            if (currentState is PlanListGlobalLoadedState &&
                                currentState.currentPlan != null) {
                              _refreshData(currentState.currentPlan!.planId);
                            }
                          }
                        });
                      },
                      child: const Row(
                        children: [
                          Text(
                            '查看',
                            style: TextStyle(
                              fontSize: 14,
                              color: AppTheme.primaryColor,
                            ),
                          ),
                          Icon(
                            Icons.arrow_forward_ios,
                            size: 12,
                            color: AppTheme.primaryColor,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        )
    );
  }

  // 信息行
  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }

  // 根据状态获取颜色
  Color _getStatusColor(String status) {
    switch (status) {
      case '待审批':
        return Colors.grey;
      case '已通过':
        return Colors.green;
      case '已驳回':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
